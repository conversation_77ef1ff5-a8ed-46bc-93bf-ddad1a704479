{"name": "titd-manage-options", "private": true, "version": "2506.1", "type": "module", "scripts": {"dev": "vite", "dev-directly": "vite dev --host", "dev-with-args-flag": "vite dev --host --port 3001", "build": "vite build", "build:dev": "vite build --mode development", "lint": "eslint .", "preview": "vite preview", "version": "node ../../scripts/auto-version.js"}, "dependencies": {"@ant-design/icons": "^6.0.0", "ahooks": "^3.9.1", "antd": "catalog:", "antd-style": "catalog:", "dayjs": "^1.11.13", "mac-scrollbar": "^0.13.8", "qs": "^6.14.0", "radash": "^12.1.1", "react": "catalog:", "react-dom": "catalog:", "react-helmet-async": "^2.0.5", "react-intl": "^7.1.11", "react-router": "^7.8.1", "recoil": "^0.7.7", "@titd/publics": "workspace:*"}}