import qs from 'qs';

export const DEFAULT_CONFIGS = {
  SYSTEM_TYPE: Number(import.meta.env.VITE_PUBLIC_SYSTEM_TYPE),  // 系统类型: 0期权,1期货,2现货
  SERVER_ID: 0,
  PAGE: 1,
  LIMIT: 15,
  USER: null,
  SHOW_CHILD: !!Number(import.meta.env.VITE_PUBLIC_TITD_SYSTEMID),  // 系统账户
  SHOW_HISTORY: !!Number(import.meta.env.VITE_PUBLIC_TITD_HISTORY),  // 场上历史数据
  SHOW_MARGINFINANCING: !!Number(import.meta.env.VITE_PUBLIC_TITD_MARGINFINANCING),  // 融资融券

  // 特别版本制定
  PUB_REQUIRED: !!Number(import.meta.env.VITE_PUBLIC_TITD_PUB),  // 业务PBU、营业部代码必填
};

export const SYSTEM_NAME = [
  ['opt', 'ETF期权'],
  ['fut', '期货'],
  ['stk', '现货'],
][DEFAULT_CONFIGS.SYSTEM_TYPE];

export const THEME_OPTIONS = [
  { label: '跟随系统', value: 'auto' },
  { label: '浅色主题', value: 'light' },
  { label: '深色主题', value: 'dark' },
];

export const LANG_OPTIONS = [
  { label: '跟随系统', value: 'auto' },
  { label: '简体中文', value: 'zh-CN' },
  { label: 'English', value: 'en-US' },
];

export const MESSAGE_TYPE = {
  // 系统发布(101-150)
  ExConnect: 101, // 交易所连接情况
  SysStatus: 102, // 系统运行情况
  UserInfo: 103,  // 用户信息
  Log: 104,       // 日志信息
  TdSvr: 105,     // 交易服务器列表

  // TITD 柜台数据库管理(200-299)
  UserLogin: 201,        // 登录
  UserLogout: 202,       // 登出
  UpdPassword: 203,      // 重置密码
  InsAppId: 204,         // AppId
  UpdAppId: 205,
  DelAppId: 206,
  QryAppId: 207,
  InsUserInfo: 208,      // 用户信息
  UpdUserInfo: 209,
  DelUserInfo: 210,
  QryUserInfo: 211,
  InsUserClient: 212,    // 用户与交易编码/股东代码对应关系信息
  UpdUserClient: 213,    // 先删除再插入
  DelUserClient: 214,
  QryUserClient: 215,
  InsTradeTemplate: 216, // 权限模版
  UpdTradeTemplate: 217,
  DelTradeTemplate: 218,
  QryTradeTemplate: 219,
  InsTradeRight: 220,    // 交易权限
  UpdTradeRight: 221,
  DelTradeRight: 222,
  QryTradeRight: 223,
  InsFrozenFunds: 224,   // 资金分配详情
  UpdFrozenFunds: 225,
  DelFrozenFunds: 226,
  QryFrozenFunds: 227,

  QryImportClient: 237,  // 系统导入的交易编码/股东代码
  QryAllFunds: 238,      // 账户总资金
  ImportLog: 239,        // 上场确认
  QryProduct: 240,       // 品种
  QrySysLog: 241,        // 操作日志
  InsAdminUser: 242,     // 管理用户
  DelAdminUser: 243,
  UpdAdminUser: 244,
  QryAdminUser: 245,

  OwTdSvr: 255,           // 数据概况
  OwUserClient: 256,      // 用户与交易编码/股东代码
  InsUserRisk: 257,       // 风控用户
  DelUserRisk: 258,
  UpdUserRisk: 259,
  QryUserRisk: 260,
  InsUserClientRisk: 261, // 风控用户与交易编码/股东代码对应关系
  QryInstrumentType: 262, // 查询合约类型(现货)
  QrySubmitInfo: 263,     // 看穿式监管
  DownloadSubmitInfo: 264,
  CheckSession: 265,      // 检测 Session 是否过期

  InsCommiTemplate: 266,       // 现货手续费模版
  DelCommiTemplate: 267,
  UpdCommiTemplate: 268,
  QryCommiTemplate: 269,
  InsCommiTemplateUser: 270,   // 现货手续费模版与用户对应关系
  DelCommiTemplateUser: 271,
  QryCommiTemplateUser: 272,
  UpdatePassword: 273,     // 修改密码
  SegmentSvr: 274,         // 集群分区
  AddAccountAlloct: 275,   // 资金分配
  DelAccountAlloct: 276,
  UpdAccountAlloct: 277,
  QryAccountAlloct: 278,
  UserInfoDetail: 279,     // 用户信息一次性提交
  UserInfoCheck: 280,      // 用户信息检查
  QryProductStk: 281,      // 品种
  QryUserInfoDetail: 282,  // 查询用户信息

  // InsSubAccount: 283,      // 子资金账户
  // DelSubAccount: 284,
  UpdSubAccount: 285,      // 子账户修改金额
  QrySubAccount: 286,
  InsSubClient: 287,       // 子用户
  DelSubClient: 288,
  // UpdSubClient: 289,
  QrySubClient: 290,
  QryMainUserInfo: 299,    // 主用户查询

  // TITD 柜台内存管理(300-399)
  DepositWithdraw: 301,     // 入金、出金
  // Deposit: 302,             // 入金
  UpdUserPassword: 303,     // 重置用户密码
  MemUpdClientStatus: 304,  // 修改用户/交易状态
  // MemQryAdminLog: 306,      // 管理日志
  // MemQryLoginLog: 307,      // 登录日志
  MemQrySession: 308,       // Session日志
  MemQryTradeInfo: 309,     // 成交信息
  MemQryAccount: 310,       // 资金
  MemUpdAccount: 311,
  // MemQryClientAccount: 312, // 交易资金
  // MemQryClientRight: 313,   // 交易权限
  MemQryCombExerc: 314,     // 组合行权
  MemQryCommiMargin: 315,   // 保证金/手续费
  MemQryInstrument: 316,    // 合约
  MemQryOmlInsert: 317,     // 组合委托
  MemQryOrderAction: 318,   // 撤单
  MemQryOrderInsert: 319,   // 期货、期权报单请求
  MemQryPosition: 320,      // 持仓
  MemQryPositionComb: 321,  // 组合持仓
  MemQryPositionDtl: 322,   // 持仓详情
  MemQryQuoteInsert: 323,   // 期货、期权报价请求
  // MemQryRight: 324,         // 权限
  MemQryRisk: 325,          // 风控
  MemQryStkInsert: 326,     // 证券报价
  MemQryUserClient: 327,    // 用户交易编码/股东代码
  MemQryUserInfo: 328,      // 用户信息
  MemUpdUserInfo: 329,
  MemQryRtnExerc: 330,      // 行权通知
  MemQryRtnOml: 331,        // 组合通知
  MemQryRtnOrder: 332,      // 期货、期权报单查询
  MemQryRtnOrderStk: 333,   // 证券报单查询
  MemQryRtnQuote: 334,      // 报价查询
  MemQryRtnTrade: 335,      // 期货、期权成交查询
  MemQryRtnTradeStk: 336,   // 证券成交查询
  // MemQryRtnPrvSequence: 337,
  MemUpdPosition: 338,      // 修改持仓
  MemQryServer: 339,        // 交易服务器查询
  MemSelfTrade: 340,        // 自成交
  MemExerc: 341,            // 行权
  MemUpdSession: 342,       // 删除 Session
  // MemUpdRisk: 343,          // 修改风控
  UpdClientStatus: 343,     // 修改黑名单
  MemQryRtnWithDraw: 344,   // 出入金通知
  UploadAccount: 345,       // 上传资金文件
  UploadPosition: 346,      // 上传持仓文件
  CheckAccount: 347,        // 校验 TI 与 CTP 资金数据
  CheckPosition: 348,       // 校验 TI 与 CTP 持仓数据
  DownloadAccount: 349,     // 资金导出
  DownloadPosition: 350,    // 持仓导出
  MemQryMarginPrice: 351,   // 查询保证金计算相关参数
  MemQryBusInsert: 361,     // 查询上交所非交易业务报单请求
  // MemQryBusTrade: ,        // 查询上交所非交易业务成交请求
  MemQryRtnOrderBus: 362,     // 查询上交所非交易业务委托回报
  MemQryRtnTradeBus: 363,     // 查询上交所非交易业务成交回报
  QryAllAccount: 364,         // 查询资金信息
  MemQryClientStatus: 368,    // 查询场上限购额度
  MemUpdOrderTimeOut: 369,    // 订单超时
  MemQryOrderTimeOut: 370,
  MemQryCreditPosition: 372,  // 两融持仓
  MemQryCredit: 373,          // 信用额度
  MemQryCashAccount: 374,     // 资金头寸
  MemQryCashStock: 375,       // 股份头寸
  MemUpdCredit: 376,
  MemUpdCashAccount: 377,
  MemUpdCashStock: 378,
  MemQryGateway: 379,         // 网关管理
  MemUpdGateway: 380,
  MemQryRepayDetail: 381,     // 查询还款明细
  MemUpdInstrument: 382,      // 更新合约

  MemInsBlackList: 399,       // 批量设置场上黑名单
  InsUpdAccount: 401,         // 资金划拨
  UpdUpdAccount: 402,
  QryUpdAccount: 403,
  CntUpdAccount: 404,
  // UpdClientStatus: 405,    // 黑名单
  DelClientStatus: 406,
  QryClientStatus: 407,
  InsLimitAmount: 408,        // 限购额度
  DelLimitAmount: 409,
  UpdLimitAmount: 410,
  QryLimitAmount: 411,
  QryTradeSum: 412,          // 成交汇总

  InsOpenCloseTrans: 413,    // 开平转换
  DelOpenCloseTrans: 414,
  UpdOpenCloseTrans: 415,
  QryOpenCloseTrans: 416,
  InsSelfTradeTrans: 417,    // 自成交转换
  DelSelfTradeTrans: 418,
  UpdSelfTradeTrans: 419,
  QrySelfTradeTrans: 420,
  InsLimitOrder: 421,        // 报单限额
  DelLimitOrder: 422,
  UpdLimitOrder: 423,
  QryLimitOrder: 424,

  InsSharePosition: 426,     // 股份划拨
  CntSharePosition: 427,
  QrySharePosition: 428,
  InsSalesNumb: 429,         // 营业部代码管理
  UpdSalesNumb: 430,
  QrySalesNumb: 431,
  QryAllPosition: 434,       // 查询集群持仓

  // InsBlackUser: 436,        // 黑名单用户
  DelBlackUser: 437,         // 删除
  UplBlackUser: 438,         // 用户上传
  // ImpBlackUser: 439,
  MemUplBlackUser: 440,
  ImpBlackInstr: 441,        // 导入合约、用户
  InsBlackInstr: 442,        // 新增、修改合约、用户
  // DelBlackInstr: 443,
  MemUplBlackInstr: 444,
  QryBlackInstr: 445,
  UplBlackInstr: 446,        // 合约上传
  InsRoles: 447,             // 角色管理
  DelRoles: 448,
  UpdRoles: 449,
  QryRoles: 450,
  CreateMenuJson: 451,
  QryRoleMenu: 454,
  QryMenuSimple: 456,        // 菜单管理

  UpdCreditTrans: 460,       // 调整交易所间信用额度、头寸
  QryCreditCash: 461,        // 查询信用额度、头寸调整日志
  QryAllCredit: 462,
  QryAllCash: 463,
  InsConcentrate: 464,       // 集中度设置
  DelConcentrate: 465,
  UpdConcentrate: 466,
  QryConcentrate: 467,
  QryConcentrateInstr: 468,  // 查询集中度分组合约
  QryConcentrateGroup: 469,  // 查询集中度分组
  InsAssignCreditCash: 470,  // 额度、头寸分配
  DelAssignCreditCash: 471,
  UpdAssignCreditCash: 472,
  QryAssignCreditCash: 473,
  QryCreditCashDetail: 474,
  InsGateway: 475,          // 网关管理
  DelGateway: 476,
  UpdGateway: 477,
  QryGateway: 478,
  UpdCashTrans: 479,
  ExpOrderInsert: 480,      // 导出报单
  InsFairPrice: 481,        // 证券公允价
  DelFairPrice: 482,
  UpdFairPrice: 483,
  QryFairPrice: 484,
  UpdExConctParam: 485,     // 交易所集中度控制参数
  QryExConctParam: 486,
};

const historyPathName = {
  huserinfo: 'userinfo',
  huserclient: 'userclient',
  hsession: 'session',
  haccount: 'account',
  hcommimargin: 'commimargin',
  hrisk: 'risk',
  hinstrument: 'instrument',
  hposition: 'position',
  hpositiondtl: 'positiondtl',
  horderinsert: 'orderinsert',
  horderaction: 'orderaction',
  hbusinsert: 'businsert',
  hquoteinsert: 'quoteinsert',
  htradeinfo: 'tradeinfo',
  hrtnorder: 'rtnorder',
  hrtntrade: 'rtntrade',
  hrtnorderbus: 'rtnorderbus',
  hrtntradebus: 'rtntradebus',
  hrtnwithdraw: 'rtnwithdraw',
  hloginlog: 'loginlog',
  hexsubmit: 'exsubmit',
  husersimple: 'usersimple',
  huserclientsimple: 'userclientsimple',
  hsqlpath: 'sqlpath',
};

export const SITE_URL = {
  // BASE: '/titd/protobuf',
  BASE: `${import.meta.env.VITE_PUBLIC_API_URL}/protobuf`,
  EXTEND: (msgType, svrId = DEFAULT_CONFIGS.SERVER_ID) => {
    const queryParams = {
      serverId: svrId,
      messageType: msgType,
      t: +new Date(),
    };
    return `post?${qs.stringify(queryParams)}`;
  },
  HISTORY: '/history',
  HISTORY_PATH: (pathName, params) => {
    const queryParams = params ? {
      ...params,
      t: +new Date(),
    } : {
      t: +new Date(),
    }

    return `${historyPathName[pathName]}?${qs.stringify(queryParams)}`
  },
  EXPORT_PATH: pathName => `${historyPathName[pathName]}/export?t=${+new Date()}`,
};

export const UPLOAD_URL = type => SITE_URL.BASE + '/' + SITE_URL.EXTEND(type);

const localPath = path => `${import.meta.env.MODE === 'development' ? '' : import.meta.env.VITE_PUBLIC_DATA_URL}data/${path}`;

export const PUBLIC_PATH = {
  ROLE_MENU: role => {
    if (role < 10) {
      role = '0' + role;
    }
    const addName = (DEFAULT_CONFIGS.SHOW_CHILD || DEFAULT_CONFIGS.SHOW_MARGINFINANCING) ? '_set' : '';

    return `/titddata/rolemenu${SYSTEM_NAME[0]}${addName}_${role}.json`;
  },
  IMPORT_LOG: '/lastimport.log',
  ERROR_CODE: localPath('titderrorcode.json'),
  CHANGE_LOG: localPath('changelog.json'),
  BLACK_USER: '/' + localPath('blackuser.csv'),
  RISK_INSTRUMENT: '/' + localPath('riskinstrument.csv'),
};

export const CURD = {
  StatusOkCode: 200,
  StatusOkMsg: '成功',
  StatusFailMsg: '失败',
  Insert: '新增',
  Update: '修改',
  Delete: '删除',
  Empty: '清空',
  SeleteFailMsg: '查询无数据',
  LoginFailMsg: '登录失败',
  ServerError: '服务器错误',
  Offline: '离线',
};

export const TABLE_WIDTH = {
  ID: 70,
  ACTION: 145,
  USER: 240,
};

export const MENUS_DISABLED = [4, 241, 242, 243, 244, 245, 447, 448, 449, 450, 454, 456 ];
export const MENUS_CHECKED = [99, 201, 202, 273, 339, 281];

export const TREE_ROOT = tree => [{
  key: 0,
  title: '根目录',
  value: 0,
  children: tree
}];

export const HOME_MENU = {
  key: 'dashboard',
  name: '首页',
  nameid: 'app.menu.index',
  path: '/dashboard',
  closable: false
};
