import { createStyles } from 'antd-style';

const useStyles = createStyles(({ token, css }) => ({
  content: css`
    display: flex;
    flex-direction: column;
    width: 100%;
    background-color: ${token.pageContainer?.colorBgPageContainer || transparent};
    position: relative;
    padding-block: ${token.pageContainer?.paddingBlockPageContainerContent}px;
    padding-inline: ${token.pageContainer?.paddingInlinePageContainerContent}px;
  `,
  contentHasHeader: css``,
  contentHasPageContianer: css`
    padding: 0;
  `,
}));

export default useStyles;
