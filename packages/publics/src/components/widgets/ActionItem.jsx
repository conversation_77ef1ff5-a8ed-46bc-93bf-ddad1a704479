import { Tooltip } from 'antd';
import { createStyles } from 'antd-style';

const useStyles = createStyles(({ token, css }) => ({
  actionItem: css`
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding-block: 0;
    padding-inline: 2px;
    color: ${token.colorTextSecondary};
    font-size: 16px;
    cursor: pointer;
    border-radius: ${token.borderRadius}px;
    transition: all .3s;

    > * {
      padding-inline: 6px;
      padding-block: 6px;
      border-radius: ${token.borderRadius}px;

      &:hover {
        background-color: ${token.colorBgTextHover};
      }
    }
  `,
}));

const ActionItem = props => {
  const {
    onClick,
    title,
    children,
  } = props;

  const { styles } = useStyles();

  return (
    <div className={styles.actionItem} onClick={onClick}>
      {title ? (
        <Tooltip title={title}>{children}</Tooltip>
      ) : (
        {children}
      )}
    </div>
  )
}

export { ActionItem };
