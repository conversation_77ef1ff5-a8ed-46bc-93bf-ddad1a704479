import { Switch } from 'antd';

const switchConfirm = props => {
  const {
    modal,
    name,
    onOk,
  } = props;

  return modal.confirm({
    title: `确定更改“ ${name} ”状态吗？`,
    okText: '确定',
    cancelText: '取消',
    onOk: onOk,
  });
}

const TableSwitch = (modal,text, record, name, onChange, dict, checked) => (
  <Switch
    checked={text === checked}
    checkedChildren={dict[1]}
    unCheckedChildren={dict[0]}
    onClick={checked => switchConfirm({
      name: record[name],
      onOk: () => onChange(record, checked)
    })}
  />
)

export default TableSwitch;
