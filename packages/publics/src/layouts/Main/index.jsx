import { useState, useMemo, useContext } from 'react';
import { useNavigate } from 'react-router';
import { Layout, Grid } from 'antd';
import { ThemeProvider } from 'antd-style';
import { GlobalScrollbar } from 'mac-scrollbar';
import Omit from 'omit.js';
import useMergedState from 'rc-util/lib/hooks/useMergedState';
import { getMatchMenu } from '@umijs/route-utils';
// import { useIntl } from 'react-intl';
import { useRequest } from 'ahooks';

import { useSetRecoilState } from 'recoil';
import { exchangeState } from '@titd/publics/states';

import { AntdStyleRegistry } from '@titd/publics/libs';
import { RouteContext } from '@titd/publics/context';
import { PageLoading } from '@titd/publics/pages';
import { WrapContent, Header, Footer, SiderMenu } from '@titd/publics/components';

import { consts, sessionParams, defaultSettings, Request, getList } from '@titd/publics/utils';
import { clearMenuItem, getMenuData, getBreadcrumbProps } from '@titd/publics/components';

import { formateMenu } from './menu-utils';

import { GlobalStyle, useStyles } from './style';
import 'mac-scrollbar/dist/mac-scrollbar.css';

const { useBreakpoint } = Grid;
const { PUBLIC_PATH } = consts;
const { DEFAULT_SESSION_PARAMS } = sessionParams;
const { getServerExchange } = getList;

// Header
const headerRender = (props, matchMenuKeys) => {
  if (props.headerRender === false || props.pure) {
    return null;
  }

  // <TiHeader
  //   isMobile={mobile}
  //   pathSnippets={pathSnippets}
  //   theme={customTheme}
  //   collapsed={collapsed}
  //   collapsedToggle={() => setCollapsed(!collapsed)}
  //   actionsArray={[{
  //     title: '测试',
  //     onClick: () => console.log('test'),
  //     children: <FullscreenOutlined />
  //   }]}
  //   avatarProps={{
  //     userInfo: {
  //       name: '测试',
  //       id: 'test',
  //       role: {
  //         id: 1,
  //         name: '超级管理员',
  //         sName: '管理',
  //         color: '#f56a00'
  //       },
  //     },
  //     dropMenu: dropMenu,
  //   }}
  // />

  return (
    <Header
      matchMenuKeys={matchMenuKeys}
      {...props}
    />
  );
}

// Footer
const footerRender = props => {
  if (props.footerRender === false || props.pure) {
    return null;
  }

  return (
    <Footer {...props} />
  );
}

// 侧边栏
const siderMenuRender = (props, matchMenuKeys) => {
  const {
    layout,
    isMobile,
    selectedKeys,
    openKeys,
    splitMenus,
    suppressSiderWhenMenuEmpty,
    menuRender,
  } = props;

  if (menuRender === false || props.pure) {
    return null;
  }
  let { menuData } = props;

  // 分割菜单
  if (splitMenus && (openKeys !== false && layout === 'mix') && !isMobile) {
    const [key] = selectedKeys || matchMenuKeys;
    if (key) {
      menuData = props.menuData?.find(item => item.key === key)?.children || [];
    } else {
      menuData = [];
    }
  }

  const clearMenuData = clearMenuItem(menuData || []);
  if (
    clearMenuData &&
    clearMenuData?.length < 1 &&
    (splitMenus || suppressSiderWhenMenuEmpty)
  ) {
    return null;
  }

  if (layout === 'top' && !isMobile) {
    return (
      <SiderMenu
        matchMenuKeys={matchMenuKeys}
        {...props}
        hide
      />
    );
  }

  const defaultDom = (
    <SiderMenu
      matchMenuKeys={matchMenuKeys}
      {...props}
      menuData={clearMenuData}
    />
  );
  if (menuRender) {
    return menuRender(props, defaultDom);
  }

  return defaultDom;
}

// 标题
// const defaultPageTitleRender = (pageProps, props) => {
//   const { pageTitleRender } = props;
//   const pageTitleInfo = getPageTitleInfo(pageProps);

//   if (pageTitleRender === false) {
//     return {
//       title: props.title || '',
//       id: '',
//       pageName: '',
//     }
//   }

//   if (pageTitleRender) {
//     const title = pageTitleRender(
//       pageProps,
//       pageTitleInfo.title,
//       pageTitleInfo,
//     );
//     if (typeof title === 'string') {
//       return getPageTitleInfo({
//         ...pageTitleInfo,
//         title,
//       });
//     }
//   }
//   return pageTitleInfo;
// }

const getPaddingInlineStart = (hasLeftPadding, collapsed, siderWidth) => {
  if (hasLeftPadding) {
    return collapsed ? 64 : siderWidth;
  }
  return 0;
}

const BaseLayout = props => {
  const {
    children,
    location = { pathname: '/' },
    style,
    contentStyle,
    route,
    defaultCollapsed,
    onCollapse: propsOnCollapse,
    siderWidth: propsSiderWidth,
    menu,
    siderMenuType,
    isChildrenLayout: propsIsChildrenLayout,
    menuDataRender,
    actionRef,
    bgLayoutImgList,
    formatMessage: propsFormatMessage,
    loading,
    fixSiderbar,
  } = props || {};

  const { styles, cx, theme } = useStyles();

  const defaultParams = DEFAULT_SESSION_PARAMS();
  const navigate = useNavigate();
  const setExchange = useSetRecoilState(exchangeState);
  const colSize = useBreakpoint();

  // 侧边栏宽度
  const siderWidth = useMemo(() => {
    if (propsSiderWidth) return propsSiderWidth;
    if (props.layout === 'mix') return 215;
    return 256;
  }, [props.layout, propsSiderWidth]);

  const menuRequest = async () => {
    const localUser = props.avatarProps?.userInfo;
    // 获取原始菜单
    const resp = await Request.get(`${PUBLIC_PATH.ROLE_MENU(localUser?.role)}?t=${+new Date()}`, {
      baseURL: '',
    });
    const { data: respData } = resp;
    const originMenu = respData?.menu || [];

    // 获取服务器列表
    const servers = await getServerExchange(Request.post, defaultParams, '', navigate, setExchange);

    return formateMenu(originMenu, servers);
  }
  const { data, mutate, loading: isLoading } = useRequest(menuRequest);

  const menuInfoData = useMemo(() => getMenuData(
    data || route?.children || [],
    menu,
    propsFormatMessage,
    menuDataRender,
  ), [propsFormatMessage, menu, menuDataRender, data, route?.children]);
  const { breadcrumb, breadcrumbMap, menuData = [] } = menuInfoData || {};
  // console.log('menuInfoData', menuInfoData);

  const matchMenus = useMemo(() => getMatchMenu(location.pathname || '/', menuData || [], true), [location.pathname, menuData]);
  const matchMenuKeys = useMemo(() => Array.from(
    new Set(matchMenus.map(item => item.key || item.path || '')),
  ), [matchMenus]);

  // 是否手机模式
  const isMobile = useMemo(() => {
    return colSize.xs && !props.disableMobile;
  }, [colSize, props.disableMobile]);

  // 'fix'菜单，计算 padding
  const hasLeftPadding = props.layout !== 'top' && !isMobile;

  // 折叠侧边栏
  const [collapsed, onCollapsed] = useMergedState(() => {
    if (defaultCollapsed !== undefined) return defaultCollapsed;
    if (isMobile) return true;
    if (colSize.md) return true;
    return false;
  }, {
    value: props.collapsed,
    onChange: propsOnCollapse,
  });

  const defaultProps = Omit({
    ...props,
    siderWidth,
    propsFormatMessage,
    breadcrumb,
    menu: {
      ...menu,
      type: siderMenuType || menu?.type,
      // loading: menuLoading,
    },
    layout: props.layout,
  }, ['className', 'style', 'breadcrumbRender']);

  // gen page title
  // const pageTitleInfo = defaultPageTitleRender({
  //   pathname: location.pathname,
  //   ...defaultProps,
  //   breadcrumbMap,
  // }, props);

  // gen breadcrumb for pageHeader
  const breadcrumbProps = getBreadcrumbProps({
    ...defaultProps,
    breadcrumbRender: props.breadcrumbRender,
    breadcrumbMap,
  }, props);

  // render sider dom
  const siderMenuDom = siderMenuRender({
    ...defaultProps,
    menuData,
    isMobile,
    collapsed,
    onCollapsed,
  }, matchMenuKeys);

  // render header dom
  const headerDom = headerRender({
    ...defaultProps,
    children: null,
    hasSiderMenu: !!siderMenuDom,
    menuData,
    isMobile,
    collapsed,
    onCollapsed,
  }, matchMenuKeys);

  // render footer dom
  const footerDom = footerRender({
    ...defaultProps,
    isMobile,
    collapsed,
  });

  // 计算 slider 宽度
  const leftSiderWidth = getPaddingInlineStart(
    !!hasLeftPadding,
    collapsed,
    siderWidth,
  );

  const { isChildrenLayout: contextIsChildrenLayout } =
    useContext(RouteContext);

  // 如果 props 中定义，以 props 为准
  const isChildrenLayout =
    propsIsChildrenLayout !== undefined
      ? propsIsChildrenLayout
      : contextIsChildrenLayout;

  const className = cx(
    `${props.prefixCls}-main-layout`,
    styles.layout,
    styles.fullContainer,
    `screen-${colSize}`,
    props.layout === 'top' ? styles.layoutTopMenu : '',
    isChildrenLayout ? styles.layoutIsChildren : '',
    fixSiderbar ? styles.layoutFixSiderbar : '',
  );

  const [hasFooterToolbar, setHasFooterToolbar] = useState(false);
  const [hasPageContainer, setHasPageContainer] = useState(0);

  const bgImgStyleList = useMemo(() => {
    if (bgLayoutImgList?.length > 0) {
      return bgLayoutImgList.map((item, index) => (
        <img
          key={index}
          src={item.src}
          alt={`bg_${index}`}
          style={{
            position: 'absolute',
            ...item,
          }}
        />
      ));
    }
    return null;
  }, [bgLayoutImgList]);

  return (
    <RouteContext.Provider
      value={{
        ...defaultProps,
        breadcrumb: breadcrumbProps,
        menuData,
        isMobile,
        collapsed,
        hasPageContainer,
        setHasPageContainer,
        isChildrenLayout: true,
        // title: pageTitleInfo.pageName,
        hasSiderMenu: !!siderMenuDom,
        hasHeader: !!headerDom,
        siderWidth: leftSiderWidth,
        hasFooter: !!footerDom,
        hasFooterToolbar,
        setHasFooterToolbar,
        // pageTitleInfo,
        matchMenus,
        matchMenuKeys,
        // currentMenu,
      }}
    >
      {props.pure ? (
        <>{children}</>
      ) : (
        <div className={className}>
          <GlobalScrollbar />
          {bgImgStyleList || theme?.bgLayout ? (
            <div className={styles.bgList}>{bgImgStyleList}</div>
          ) : null}
          <ThemeProvider
            theme={{
              // token: {
              //   controlHeightLG:
              //     token.sider?.menuHeight || token.controlHeightLG,
              // }
            }}
          >
            <GlobalStyle />
            <Layout style={{
              minHeight: '100%',
              flexDirection: siderMenuDom ? 'row' : undefined,
            }}>
              {siderMenuDom}
              <div className={styles.container}>
                {headerDom}
                <WrapContent
                  hasPageContainer={hasPageContainer}
                  isChildrenLayout={isChildrenLayout}
                  hasHeader={!!headerDom}
                >
                  {loading ? <PageLoading formatMessage={propsFormatMessage} /> : children}
                </WrapContent>
                {footerDom}
                {hasFooterToolbar && (
                  <div className={styles.hasFooter} />
                )}
              </div>
            </Layout>
          </ThemeProvider>
        </div>
      )}
    </RouteContext.Provider>
  )
}

const MainLayout = props => (
  <AntdStyleRegistry {...defaultSettings}>
    <BaseLayout
      {...defaultSettings}
      // location={location}
      {...props}
    />
  </AntdStyleRegistry>
);

export { MainLayout };
