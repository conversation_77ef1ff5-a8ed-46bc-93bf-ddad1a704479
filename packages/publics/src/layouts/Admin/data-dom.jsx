import {
  SettingOutlined,
  ClearOutlined,
  LockOutlined,
  LogoutOutlined,
  FullscreenOutlined,
} from '@ant-design/icons';

import { LayoutBg1, LayoutBg2 } from '@titd/publics/assets';

export const userDropMenu = [{
  label: '系统设置',
  locale: 'general.header.user.setting',
  icon: <SettingOutlined />,
  key: 0,
}, {
  label: '清除缓存',
  locale: 'general.header.user.clearcache',
  icon: <ClearOutlined />,
  key: 1,
}, {
  type: 'divider',
}, {
  label: '修改密码',
  locale: 'general.auth.updatepwd',
  icon: <LockOutlined />,
  key: 2,
}, {
  label: '退出登录',
  locale: 'general.header.user.logout',
  icon: <LogoutOutlined />,
  key: 3,
}];

export const actionsGroup = [{
  title: '测试',
  onClick: () => console.log('test'),
  children: <FullscreenOutlined />
}];

export const bgImgList = [{
  src: LayoutBg1,
  left: 85,
  bottom: 100,
  height: '303px',
}, {
  src: LayoutBg1,
  left: -68,
  right: -45,
  height: '303px',
}, {
  src: LayoutBg2,
  bottom: 0,
  left: 0,
  width: '331px',
}];
